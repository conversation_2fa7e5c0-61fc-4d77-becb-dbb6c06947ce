@import "tailwindcss";

:root {
  /* 基础颜色 */
  --background: #ffffff;
  --foreground: #171717;
  --font-mono: 'Maple Mono', monospace;
  --navbar-height: 40px;  /* 导航栏高度变量 */
  
  /* 侧边栏 */
  --sidebar-bg: #fff;
  --sidebar-border: #f0f0f0;
  
  /* 卡片 */
  --card-bg: #fff;
  --card-border: #eaeaea;
  --component-card: #f3f1f1;
  
  /* 文本颜色 */
  --primary-text: #171717;
  --secondary-text: #666666;
  --tertiary-text: #999999;

  --inverted-text: #b0b0b0;
  
  /* 图标颜色 */
  --icon-color: #666666;
  --icon-color-hover: #333333;
  
  /* 按钮颜色 */
  --button-text: #666666;
  --button-bg: transparent;
  --button-border: #eaeaea;
  --button-hover: #E5E7EB;
  
  /* 输入框 */
  --input-bg: #ffffff;
  --input-border: #d9d9d9;
  --input-text: #171717;
  
  /* 链接颜色 */
  --link-color: #1677ff;
  --link-hover: #4096ff;
  
  /* 代码块 */
  --code-bg: #f5f5f5;
  --code-text: #333333;
  
  /* 分割线 */
  --divider-color: #f0f0f0;
  
  /* 消息提示颜色 */
  --success-color: #52c41a;
  --info-color: #1677ff;
  --error-color: #ff4d4f;
}

[data-theme="dark"] {
  /* 基础颜色 */
  --background: #121212;
  --foreground: #f0f0f0;
  
  /* 侧边栏 */
  --sidebar-bg: #1e1e1e;
  --sidebar-border: #333333;
  --button-hover: #333333;
  
  /* 卡片 */
  --card-bg: #1e1e1e;
  --card-border: #333333;
  --component-card: #252424;
  
  /* 文本颜色 */
  --primary-text: #f0f0f0;
  --secondary-text: #b0b0b0;
  --tertiary-text: #808080;
  --inverted-text: #6e6d6d;
  
  /* 图标颜色 */
  --icon-color-dark: #b0b0b0;
  --icon-color-hover: #f0f0f0;
  
  /* 按钮颜色 */
  --button-text: #b0b0b0;
  --button-bg: transparent;
  --button-border: #333333;
  
  /* 输入框 */
  --input-bg: #2c2c2c;
  --input-border: #444444;
  --input-text: #f0f0f0;
  
  /* 链接颜色 */
  --link-color: #4096ff;
  --link-hover: #69b1ff;
  
  /* 代码块 */
  --code-bg: #2c2c2c;
  --code-text: #e0e0e0;
  
  /* 分割线 */
  --divider-color: #333333;
  
  /* 消息提示颜色 */
  --success-color: #52c41a;
  --info-color: #1677ff;
  --error-color: #ff4d4f;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--primary-text);
  font-family: Arial, Helvetica, sans-serif;
}

/* 全局文本样式 */
h1, h2, h3, h4, h5, h6 {
  color: var(--primary-text);
}

p, span, div {
  color: var(--primary-text);
}

a {
  color: var(--link-color);
}

a:hover {
  color: var(--link-hover);
}

/* 输入框全局样式 */
input, textarea, select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}

/* 代码块全局样式 */
code, pre {
  background-color: var(--code-bg);
  color: var(--code-text);
}

/* 分割线全局样式 */
hr {
  border-color: var(--divider-color);
}


/* 全局快捷键提示样式 */
.shortcut-tooltip {
  display: flex;
  align-items: center;
  padding: 4px 0;
  color: var(--tertiary-text);
}


.shortcut-icon {
  font-size: 1em !important;
  margin-left: 4px;
  margin-right: 2px;
  color: var(--tertiary-text);
}

.shortcut-plus {
  margin: 0 2px;
}

.shortcut-tooltip .shortcut-plus {
  margin: 0 2px;
  opacity: 0.7;
}

/* 定义字体 */
@font-face {
    font-family: 'Maple Mono';
    font-style: normal;
    font-weight: normal;
    src: url('/fonts/MapleMono-Regular.ttf') format('truetype');
    font-display: swap;
}

@font-face {
    font-family: 'Maple Mono';
    font-style: italic;
    font-weight: normal;
    src: url('/fonts/MapleMono-Italic.ttf') format('truetype');
    font-display: swap;
}

@font-face {
    font-family: 'Maple Mono';
    font-style: normal;
    font-weight: bold;
    src: url('/fonts/MapleMono-Bold.ttf') format('truetype');
    font-display: swap;
}

/* 在:root中定义变量 */
:root {
    --font-mono: 'Maple Mono', monospace !important;
}

/* 强制应用到Monaco编辑器 */
.monaco-editor {
    font-family: var(--font-mono) !important;
}

.monaco-editor .view-line * {
    font-family: var(--font-mono) !important;
}

/* 添加页面过渡动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-container {
  animation: fadeIn 0.5s ease-out;
}

/* 骨架屏动画 */
@keyframes skeletonPulse {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.4;
  }
}

.skeleton-pulse {
  animation: skeletonPulse 1.5s infinite ease-in-out;
}

/* 消息提示样式 */
.ant-message-notice-content {
  background-color: var(--card-bg) !important;
  color: var(--primary-text) !important;
  border: 1px solid var(--card-border) !important;
  padding: 10px 16px !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
}

[data-theme="dark"] .ant-message-notice-content {
  background-color: #1e1e1e !important;
  color: #f0f0f0 !important;
  border: 1px solid #333333 !important;
}

/* 添加全局下拉菜单样式 */
[data-theme="dark"] .ant-dropdown-menu {
  background-color: #1f1f1f !important;
  border-color: #333 !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .ant-dropdown-menu-item {
  color: #e0e0e0 !important;
}

[data-theme="dark"] .ant-dropdown-menu-item:hover {
  background-color: #333 !important;
}

[data-theme="dark"] .ant-dropdown-menu-item-icon {
  color: #b0b0b0 !important;
}

@import '../styles/globalSidebar.css';
